{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "example_client",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/example_c_program/bin/example_client",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${fileDirname}",
            "environment": [{"name": "LD_LIBRARY_PATH", "value": "/data/git/testd/build/libtestsocket/bin"}],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ]
        },
        {
            "name": "testd",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/testd/bin/testd",
            "args": ["-v","run","--sysroot", "${workspaceFolder}/build/install","/data/git/testd/packages/example_c_program/test/case_dns_lookup.json"],
            "stopAtEntry": false,
            "cwd": "${fileDirname}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                },
                {
                    "description": "Load JSON-C Printers",
                    "text": "source ${workspaceFolder}/debug/jsonc-printers.py"
                }
            ]
        },
        {
            "name": "ash",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/packages/busybox/src/busybox_unstripped",
            "args": ["ash","example/echo.sh"],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {"name": "LD_LIBRARY_PATH", "value": "/data/git/testd/build/libtestsocket/bin"}
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ]
        },
        {
            "name": "testd-ash",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/testd/bin/testd",
            "args": ["-v","--sysroot", "${workspaceFolder}/build/install", "run","${workspaceFolder}/example/case_echo_sh.json"],
            "stopAtEntry": false,
            "cwd": "${fileDirname}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                },
                {
                    "description": "Load JSON-C Printers",
                    "text": "source ${workspaceFolder}/debug/jsonc-printers.py"
                }
            ]
        },
        {
            "name": "lua",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/packages/lua/src/src/lua",
            "args": ["${workspaceFolder}/example/echo.lua"],
            "stopAtEntry": false,
            "cwd": "${fileDirname}",
            "environment": [
                {"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/packages/lua/src/src:${workspaceFolder}/build/libtestsocket/bin"}
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ]
        },
        {
            "name": "testd-lua",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/testd/bin/testd",
            "args": ["-v","run","/data/git/testd/example/case_echo_lua.json"],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                },
                {
                    "description": "Load JSON-C Printers",
                    "text": "source ${workspaceFolder}/debug/jsonc-printers.py"
                },
                {
                    "text": "set detach-on-fork off",
                    "ignoreFailures": false
                }
            ]
        },
        {
            "name": "testd-simple",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/testd/bin/testd",
            "args": ["-v","run","${workspaceFolder}/another_test.json"],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "将反汇编风格设置为 Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ]
        }

    ]
}