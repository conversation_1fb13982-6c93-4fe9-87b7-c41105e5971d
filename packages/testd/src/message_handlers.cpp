#include "message_handlers.hpp"
#include "socket.hpp"
#include "test_case.hpp"
#include <iostream>
#include <algorithm>

namespace testd {


// InitMessageHandler 实现
bool InitMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type != "init") {
        return false;
    }
    
    // 获取客户端
    std::shared_ptr<ClientConnection> client = nullptr;

    // 优先使用clientUUID查找
    if (!msg.clientUUID.empty()) {
        client = Context::getInstance().getClientByUUID(msg.clientUUID);
    }

    // 如果UUID查找失败，回退到sourceId查找
    if (!client) {
        auto allClients = Context::getInstance().getAllClients();
        for (const auto& c : allClients) {
            std::string clientId = c->getProgramName() + ":" + std::to_string(c->getPid());
            if (clientId == msg.sourceId) {
                client = c;
                break;
            }
        }
    }
    
    if (!client) {
        std::cerr << "Client not found for init message: " << msg.sourceId << std::endl;
        return false;
    }
    
    // 处理初始化消息
    struct json_object* dataObj = msg.getData();
    if (dataObj) {
        struct json_object* pidObj;
        if (json_object_object_get_ex(dataObj, "pid", &pidObj)) {
            int pid = json_object_get_int(pidObj);
            client->setPid(pid);
        }

        struct json_object* programObj;
        if (json_object_object_get_ex(dataObj, "program", &programObj)) {
            const char* program = json_object_get_string(programObj);
            if (program) {
                client->setProgramName(program);
            }
        }
    }

    // 对于非待测进程，自动发送run命令让程序继续执行
    // 注意：待测进程的init消息已被TestTaskInitMessageHandler以更高优先级处理
    struct json_object* runRequest = JsonProtocol::createActionRequest("run");
    if (runRequest) {
        Socket::sendJson(client, runRequest);
        json_object_put(runRequest);
        if (Context::getInstance().getConfig().isVerbose()) {
            std::cout << "Auto-sent run command to non-test program: "
                      << client->getProgramName()
                      << " (PID: " << client->getPid() << ")" << std::endl;
        }
    }
    
    return true;  // 消息已处理
}

// LogMessageHandler 实现
bool LogMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type != "log") {
        return false;
    }
    
    // 处理日志消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[LOG:" << msg.subType << "] from " << msg.sourceId 
                  << " at " << msg.timestamp << std::endl;
        
        if (msg.getData()) {
            const char* jsonStr = json_object_to_json_string(msg.getData());
            if (jsonStr) {
                std::cout << "  Data: " << jsonStr << std::endl;
            }
        }
    }
    
    return false;  // 不消费消息，让其他处理器也能处理
}

// TestVerificationHandler 实现
bool TestVerificationHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type == "test_point") {
        // 处理测试点消息
        if (Context::getInstance().getConfig().isVerbose()) {
            std::cout << "[TEST_POINT] from " << msg.sourceId << " at " << msg.timestamp << std::endl;
            if (msg.getData()) {
                const char* data = json_object_get_string(msg.getData());
                if (data) {
                    std::cout << "  Data: " << data << std::endl;
                }
            }
        }

        // 这里可以实现测试点验证逻辑
        auto currentTest = Context::getInstance().getCurrentTest();
        if (currentTest) {
            // 可以在这里添加测试点验证逻辑
            // 例如检查测试点是否符合期望的模式
        }

        return false;  // 不消费消息，让其他处理器也能处理
    }

    if (msg.type == "log" && msg.hasTag("logging")) {
        // 处理日志消息的测试验证
        auto currentTest = Context::getInstance().getCurrentTest();
        if (currentTest) {
            // 可以在这里添加日志验证逻辑
            // 例如检查日志内容是否匹配期望的模式
        }

        return false;  // 不消费消息
    }

    return false;
}

// RequestResponseHandler 实现
bool RequestResponseHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.type != "response") {
        return false;  // 不是响应消息
    }

    // 处理响应消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[RESPONSE] from " << msg.sourceId << " at " << msg.timestamp;
        if (!msg.id.empty()) {
            std::cout << " ID: " << msg.id;
        }
        if (msg.idInt != 0) {
            std::cout << " (" << msg.idInt << ")";
        }
        std::cout << " Result: " << msg.result;
        if (!msg.error.empty()) {
            std::cout << " Error: " << msg.error;
        }
        std::cout << std::endl;
    }

    // 这里可以实现请求-响应匹配逻辑
    // 例如通知等待响应的任务

    return false;  // 不消费消息，让其他处理器处理
}

// ErrorMessageHandler 实现
bool ErrorMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (msg.priority != MessagePriority::CRITICAL && !msg.hasTag("error")) {
        return false;
    }
    
    // 处理错误消息
    std::cerr << "[ERROR] from " << msg.sourceId << ": " << msg.error << std::endl;
    if (msg.getData()) {
        const char* jsonStr = json_object_to_json_string(msg.getData());
        if (jsonStr) {
            std::cerr << "  Error data: " << jsonStr << std::endl;
        }
    }
    
    return false;  // 不消费消息
}

// DebugMessageHandler 实现
bool DebugMessageHandler::handleMessage(const EnhancedMessage& msg) {
    if (!msg.hasTag("debug") && msg.type != "debug") {
        return false;
    }
    
    // 处理调试消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[DEBUG] " << msg.type << " from " << msg.sourceId << std::endl;
        if (msg.getData()) {
            const char* jsonStr = json_object_to_json_string(msg.getData());
            if (jsonStr) {
                std::cout << "  Debug data: " << jsonStr << std::endl;
            }
        }
    }
    
    return false;  // 不消费消息
}

// DefaultMessageHandler 实现
bool DefaultMessageHandler::handleMessage(const EnhancedMessage& msg) {
    // 处理所有未被其他处理器处理的消息
    if (Context::getInstance().getConfig().isVerbose()) {
        std::cout << "[DEFAULT] Unhandled message type: " << msg.type 
                  << " from " << msg.sourceId << std::endl;
    }
    
    return false;  // 不消费消息
}

// MessageHandlerFactory 实现
std::vector<std::shared_ptr<MessageHandler>> MessageHandlerFactory::createDefaultHandlers() {
    std::vector<std::shared_ptr<MessageHandler>> handlers;

    handlers.push_back(std::make_shared<InitMessageHandler>());
    handlers.push_back(std::make_shared<LogMessageHandler>());
    handlers.push_back(std::make_shared<TestVerificationHandler>());
    handlers.push_back(std::make_shared<RequestResponseHandler>());
    handlers.push_back(std::make_shared<ErrorMessageHandler>());
    handlers.push_back(std::make_shared<DebugMessageHandler>());
    handlers.push_back(std::make_shared<DefaultMessageHandler>());

    return handlers;
}

void MessageHandlerFactory::registerDefaultHandlers() {
    auto& messageSystem = MessageSystem::getInstance();

    // 注册初始化消息处理器
    MessageFilter initFilter;
    initFilter.byType("init");
    messageSystem.subscribe(initFilter, std::make_shared<InitMessageHandler>(), 100, false);

    // 注册日志消息处理器
    MessageFilter logFilter;
    logFilter.byType("log");
    messageSystem.subscribe(logFilter, std::make_shared<LogMessageHandler>(), 10, false);

    // 注册测试点消息处理器
    MessageFilter testPointFilter;
    testPointFilter.byType("test_point");
    messageSystem.subscribe(testPointFilter, std::make_shared<TestVerificationHandler>(), 70, false);

    // 注册响应消息处理器
    MessageFilter responseFilter;
    responseFilter.byType("response");
    messageSystem.subscribe(responseFilter, std::make_shared<RequestResponseHandler>(), 80, false);

    // 注册错误消息处理器
    MessageFilter errorFilter;
    errorFilter.byPriority(MessagePriority::CRITICAL);
    messageSystem.subscribe(errorFilter, std::make_shared<ErrorMessageHandler>(), 90, false);

    // 注册调试消息处理器
    MessageFilter debugFilter;
    debugFilter.byTag("debug");
    messageSystem.subscribe(debugFilter, std::make_shared<DebugMessageHandler>(), 20, false);

    // 注册默认处理器（处理所有消息）
    MessageFilter defaultFilter;  // 空过滤器匹配所有消息
    messageSystem.subscribe(defaultFilter, std::make_shared<DefaultMessageHandler>(), -100, false);
}

std::shared_ptr<MessageHandler> MessageHandlerFactory::createHandler(const std::string& type) {
    if (type == "init") {
        return std::make_shared<InitMessageHandler>();
    } else if (type == "log") {
        return std::make_shared<LogMessageHandler>();
    } else if (type == "test") {
        return std::make_shared<TestVerificationHandler>();
    } else if (type == "response") {
        return std::make_shared<RequestResponseHandler>();
    } else if (type == "error") {
        return std::make_shared<ErrorMessageHandler>();
    } else if (type == "debug") {
        return std::make_shared<DebugMessageHandler>();
    } else {
        return std::make_shared<DefaultMessageHandler>();
    }
}

// MessageHandlerManager 实现
MessageHandlerManager::~MessageHandlerManager() {
    clear();
}

void MessageHandlerManager::addHandler(std::shared_ptr<MessageHandler> handler, const MessageFilter& filter) {
    if (!handler) {
        return;
    }

    // 检查是否已经存在同名处理器
    auto it = std::find_if(handlers_.begin(), handlers_.end(),
                          [&handler](const std::pair<std::shared_ptr<MessageHandler>, std::string>& pair) {
                              return pair.first->getName() == handler->getName();
                          });

    if (it != handlers_.end()) {
        // 移除旧的处理器
        MessageSystem::getInstance().getRouter().unsubscribe(it->second);
        handlers_.erase(it);
    }

    // 添加新的处理器
    std::string subscriptionId = MessageSystem::getInstance().subscribe(
        filter, handler, handler->getPriority(), false);

    handlers_.emplace_back(handler, subscriptionId);
}

void MessageHandlerManager::removeHandler(const std::string& handlerName) {
    auto it = std::find_if(handlers_.begin(), handlers_.end(),
                          [&handlerName](const std::pair<std::shared_ptr<MessageHandler>, std::string>& pair) {
                              return pair.first->getName() == handlerName;
                          });

    if (it != handlers_.end()) {
        MessageSystem::getInstance().getRouter().unsubscribe(it->second);
        handlers_.erase(it);
    }
}

std::shared_ptr<MessageHandler> MessageHandlerManager::getHandler(const std::string& handlerName) {
    auto it = std::find_if(handlers_.begin(), handlers_.end(),
                          [&handlerName](const std::pair<std::shared_ptr<MessageHandler>, std::string>& pair) {
                              return pair.first->getName() == handlerName;
                          });

    if (it != handlers_.end()) {
        return it->first;
    }

    return nullptr;
}

void MessageHandlerManager::clear() {
    for (const auto& pair : handlers_) {
        MessageSystem::getInstance().getRouter().unsubscribe(pair.second);
    }
    handlers_.clear();
}

void MessageHandlerManager::initializeDefaultHandlers() {
    // 清空现有处理器
    clear();

    // 添加初始化消息处理器
    MessageFilter initFilter;
    initFilter.byType("init");
    addHandler(std::make_shared<InitMessageHandler>(), initFilter);

    // 添加日志消息处理器
    MessageFilter logFilter;
    logFilter.byType("log");
    addHandler(std::make_shared<LogMessageHandler>(), logFilter);

    // 添加测试验证处理器
    MessageFilter testFilter;
    testFilter.byTag("logging");
    addHandler(std::make_shared<TestVerificationHandler>(), testFilter);

    // 添加错误消息处理器
    MessageFilter errorFilter;
    errorFilter.byPriority(MessagePriority::CRITICAL);
    addHandler(std::make_shared<ErrorMessageHandler>(), errorFilter);

    // 添加请求响应处理器
    MessageFilter responseFilter;
    responseFilter.byCustom([](const EnhancedMessage& msg) {
        return msg.idInt != 0 || !msg.id.empty();
    });
    addHandler(std::make_shared<RequestResponseHandler>(), responseFilter);

    // 添加调试消息处理器
    MessageFilter debugFilter;
    debugFilter.byTag("debug");
    addHandler(std::make_shared<DebugMessageHandler>(), debugFilter);

    // 添加默认处理器
    MessageFilter defaultFilter;  // 空过滤器匹配所有消息
    addHandler(std::make_shared<DefaultMessageHandler>(), defaultFilter);
}

} // namespace testd
