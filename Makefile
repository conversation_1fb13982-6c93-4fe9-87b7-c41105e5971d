# 顶层Makefile
# 用于编译packages目录下的各个包

# 定义编译工作目录
BUILD_DIR := build

# 获取packages目录下的所有包
PACKAGES := $(notdir $(wildcard packages/*))

# 默认目标
.PHONY: all
all: $(PACKAGES)

# 创建build目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

# 为每个包定义目标
.PHONY: $(PACKAGES)
$(PACKAGES): $(BUILD_DIR)
	@echo "编译包: $@"
	mkdir -p $(BUILD_DIR)/$@
	@if [ -f packages/$@/Makefile ]; then \
		# 尝试使用不同的方式传递构建目录 \
		( cd packages/$@ && \
		  export BUILD_DIR=$(abspath $(BUILD_DIR)/$@) && \
		  export OBJ_DIR=$(abspath $(BUILD_DIR)/$@)/obj && \
		  export BIN_DIR=$(abspath $(BUILD_DIR)/$@)/bin && \
		  export DESTDIR=$(abspath $(BUILD_DIR)/$@)/install && \
		  $(MAKE) \
		    BUILD_DIR=$(abspath $(BUILD_DIR)/$@) \
		    OBJ_DIR=$(abspath $(BUILD_DIR)/$@)/obj \
		    BIN_DIR=$(abspath $(BUILD_DIR)/$@)/bin \
		) || echo "编译 $@ 失败"; \
	else \
		echo "包 $@ 没有Makefile"; \
	fi

lua:
	mkdir -p $(BUILD_DIR)/lua/bin
	cd packages/lua/src && make clean linux
	cp packages/lua/src/src/lua $(BUILD_DIR)/lua/bin/
	cp packages/lua/src/src/luac $(BUILD_DIR)/lua/bin/
	cp packages/lua/src/src/liblua.so* $(BUILD_DIR)/lua/bin/

busybox:
	mkdir -p $(BUILD_DIR)/busybox/bin
	cd packages/busybox/src && make clean
	cp packages/busybox/config_ash_only packages/busybox/src/.config
	cd packages/busybox/src && make
	cp packages/busybox/src/busybox $(BUILD_DIR)/busybox/bin/
	ln -sf busybox $(BUILD_DIR)/busybox/bin/ash

# 清理目标
.PHONY: clean
clean:
	@echo "清理所有包"
	rm -rf $(BUILD_DIR)
	@for pkg in $(PACKAGES); do \
		if [ -f packages/$$pkg/Makefile ]; then \
			echo "清理包: $$pkg"; \
			$(MAKE) -C packages/$$pkg clean || echo "清理 $$pkg 失败"; \
		fi \
	done

# 安装目标
.PHONY: install
install: all
	@echo "安装所有包"
	mkdir -p $(BUILD_DIR)/install/usr/bin
	@for pkg in $(PACKAGES); do \
		cp -r $(BUILD_DIR)/$$pkg/bin/* $(BUILD_DIR)/install/usr/bin/; \
	done

# 单独编译某个包
.PHONY: $(addprefix build-,$(PACKAGES))
$(addprefix build-,$(PACKAGES)): build-%: $(BUILD_DIR)
	@echo "单独编译包: $*"
	mkdir -p $(BUILD_DIR)/$*/bin
	mkdir -p $(BUILD_DIR)/$*/obj
	@if [ -f packages/$*/Makefile ]; then \
		( cd packages/$* && \
		  export BUILD_DIR=$(abspath $(BUILD_DIR)/$*) && \
		  export OBJ_DIR=$(abspath $(BUILD_DIR)/$*)/obj && \
		  export BIN_DIR=$(abspath $(BUILD_DIR)/$*)/bin && \
		  export DESTDIR=$(abspath $(BUILD_DIR)/$*)/install && \
		  $(MAKE) \
		    BUILD_DIR=$(abspath $(BUILD_DIR)/$*) \
		    OBJ_DIR=$(abspath $(BUILD_DIR)/$*)/obj \
		    BIN_DIR=$(abspath $(BUILD_DIR)/$*)/bin \
		) || echo "编译 $* 失败"; \
	else \
		echo "包 $* 没有Makefile"; \
	fi

build-lua: lua
build-busybox: busybox

# 单独清理某个包
.PHONY: $(addprefix clean-,$(PACKAGES))
$(addprefix clean-,$(PACKAGES)): clean-%:
	@echo "单独清理包: $*"
	rm -rf $(BUILD_DIR)/$*

# 单独安装某个包
.PHONY: $(addprefix install-,$(PACKAGES))
$(addprefix install-,$(PACKAGES)): install-%: build-%
	@echo "单独安装包: $*"
	mkdir -p $(BUILD_DIR)/install/usr/bin
	cp -r $(BUILD_DIR)/$*/bin/* $(BUILD_DIR)/install/usr/bin/

install-testd: build-testd
	killall testd || true
	cp -r $(BUILD_DIR)/testd/bin/* $(BUILD_DIR)/install/usr/bin/

.PHONY: tests test-c test-lua test-ash test-prepare
tests: test-c test-lua test-ash

test-prepare:
	mkdir -p /var/run/testd

test-c: install-example_c_program install-libtestsocket install-testd
	$(BUILD_DIR)/install/usr/bin/testd -v --sysroot $(BUILD_DIR)/install run packages/example_c_program/test/case_dns_lookup.json

test-lua: install-lua install-libtestsocket install-testd
	$(BUILD_DIR)/install/usr/bin/testd -v --sysroot $(BUILD_DIR)/install run example/case_echo_lua.json

test-ash: install-busybox install-libtestsocket install-testd
	$(BUILD_DIR)/install/usr/bin/testd -v --sysroot $(BUILD_DIR)/install run example/case_echo_sh.json

# 帮助信息
.PHONY: help
help:
	@echo "顶层Makefile帮助信息"
	@echo "可用目标:"
	@echo "  all                - 编译所有包"
	@echo "  clean              - 清理所有包"
	@echo "  install            - 安装所有包"
	@echo "  build-<包名>       - 编译指定的包"
	@echo "  clean-<包名>       - 清理指定的包"
	@echo "  install-<包名>     - 安装指定的包"
	@echo "  tests              - 运行所有测试"
	@echo "  test-c             - 运行C程序测试"
	@echo "  test-lua           - 运行Lua测试"
	@echo "  test-ash           - 运行ash测试"
	@echo "  help               - 显示此帮助信息"
	@echo ""
	@echo "编译工作目录:"
	@echo "  $(BUILD_DIR)       - 所有包的编译输出将放在此目录下的相应子目录中"
	@echo ""
	@echo "可用的包:"
	@for pkg in $(PACKAGES); do \
		if [ -f packages/$$pkg/Makefile ]; then \
			echo "  $$pkg"; \
		fi \
	done
	@echo ""
	@echo "注意:"
	@echo "  1. 每个包的编译输出将放在 $(BUILD_DIR)/<包名> 目录下"
	@echo "  2. 所有包的安装输出将放在 $(BUILD_DIR)/install 目录下"
	@echo "  3. 如果包的Makefile不支持某些参数，可能会导致编译失败"